#二次开发核心需求
    ##必须考虑复用、或在现有方法上扩展、或进行方法重载，保证最小粒度改动，减少重复代码，考虑后续官方更新版本。
    ##不需要考虑企业级应用，主要是5人小团队使用，但功能必须齐全
    ##不需要LLM本地服务
    ##1、OPENAI实现自定义第三方API_URL以及KEY配置方法
    ##2、不需要API key验证
    ##3、完整官方项目的核心功能
        ###Contextual Add (ADD v2) https://docs.mem0.ai/platform/features/contextual-add
        ###advanced-retrieval https://docs.mem0.ai/platform/features/advanced-retrieval
        ###criteria-retrieval https://docs.mem0.ai/platform/features/criteria-retrieval
        ###selective-memory https://docs.mem0.ai/platform/features/selective-memory
        ###categories https://docs.mem0.ai/platform/features/custom-categories
        ###instructions https://docs.mem0.ai/platform/features/custom-instructions
        ###direct-import https://docs.mem0.ai/platform/features/direct-import
        ###memory-export https://docs.mem0.ai/platform/features/memory-export
        ###timestamp https://docs.mem0.ai/platform/features/timestamp
        ###feedback-mechanism   https://docs.mem0.ai/platform/features/feedback-mechanism        
##完整的官方API路由功能 https://docs.mem0.ai/api-reference
##不需要考虑实现openmemory
##docker部署
##创造记忆符合官方逻辑 https://docs.mem0.ai/platform/quickstart
    ###（When passing user_id, memories are primarily created based on user messages, but may be influenced by assistant messages for contextual understanding. For example, in a conversation about food preferences, both the user’s stated preferences and their responses to the assistant’s questions would form user memories. Similarly, when using agent_id, assistant messages are prioritized, but user messages might influence the agent’s memories based on context. This approach ensures comprehensive memory creation while maintaining appropriate attribution to either users or agents.）
##自定义数据目录路径
##使用相对路径
##ubuntu上部署，自动化一键部署脚本可交互部署&管理工具
    ###菜单设计建议示例
    ###、1、docker部署（推荐）
        ├──完整模式（
        │	├──部署选项:
        │		├──1) 完整模式部署Mem0
        │       ├──2) 重新构建服务	
        2、Mem0服务管理
        │	├──1) 停止服务
        │	├── 2) 重启服务
        │	├── 3) 查看服务状态
        │	├──4）查看日志
        │		├──1) 实时日志查看
        │		├──2) 历史日志查看
        │		├──3) 日志分析和导出
        │		├──4) 日志清理
        │	├──5) 性能监控
        3、Mem0系统管理
        │	├──1) 系统清理
        │	├──2) 磁盘空间分析
        │	├──3) 健康检查
        │	├──4) 备份管理
        │	├──5) 系统时间同步
        │	├──6) 检查容器时间
        │   
        4、系统命令帮助