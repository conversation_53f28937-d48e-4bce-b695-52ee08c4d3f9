1. 基本使用方式Permalink
添加个人信息和偏好Permalink
Copy code请帮我记住以下信息：
- 我是一个全栈开发者
- 主要技术栈：TypeScript、React、Node.js、Next.js
- 数据库偏好：PostgreSQL 和 MongoDB
- 样式框架：喜欢使用 Tailwind CSS
- 代码风格：偏好函数式编程，使用 ESLint 和 Prettier

记录项目信息Permalink
Copy code请记录我当前的项目：
项目名称：电商管理系统
技术栈：Next.js + TypeScript + Prisma + PostgreSQL
部署平台：Vercel
主要功能：商品管理、订单处理、用户认证

添加学习和工作习惯Permalink
Copy code请记住我的工作习惯：
- 使用 VS Code 插件：Auto Rename Tag、ES7+ React Snippets
- 代码提交习惯：遵循 Conventional Commits 规范
- 测试偏好：Jest + React Testing Library
- 部署流程：GitHub Actions + Vercel 自动部署

2. 查询和检索信息Permalink
询问之前记录的信息Permalink
Copy code我之前提到过使用什么数据库吗？

Copy code提醒我一下我当前项目的技术栈是什么？

Copy code我的代码风格偏好是什么？

3. 项目相关的记忆Permalink
记录具体需求Permalink
Copy code对于电商项目，请记住这些需求：
- 需要实现购物车功能，支持商品规格选择
- 支付集成：Stripe 和支付宝
- 管理后台需要数据分析图表
- 移动端适配是必需的

记录解决方案和最佳实践Permalink
Copy code请记住这个解决方案：
问题：Next.js 中图片优化
解决方案：使用 next/image 组件，配置 domains 白名单，设置适当的 sizes 属性
最佳实践：为不同屏幕尺寸提供不同图片规格

4. 高级用法Permalink
关联信息查询Permalink
Copy code基于我之前提到的技术偏好，为我的电商项目推荐一个状态管理方案

个性化建议Permalink
Copy code根据我的编程习惯和项目需求，帮我设计一个合适的文件夹结构

学习进度跟踪Permalink
Copy code请记录我的学习计划：
本月目标：深入学习 TypeScript 高级类型
已完成：泛型、条件类型
进行中：映射类型和模板字面量类型
下一步：学习 TypeScript 装饰器

5. 实际工作场景Permalink
代码审查标准Permalink
Copy code请记住我的代码审查清单：
1. 是否有类型安全问题
2. 组件是否可复用
3. 性能优化：useMemo、useCallback 的合理使用
4. 错误处理是否完善
5. 测试覆盖率是否达标

项目部署清单Permalink
Copy code记录我的部署前检查清单：
- 环境变量配置检查
- 数据库迁移脚本运行
- 静态资源 CDN 配置
- SSL 证书有效性
- 监控和日志配置

6. 验证记忆效果Permalink
几天后，您可以测试 Graphiti 的记忆能力：

Copy code我应该使用什么技术栈来开发新项目？

Copy code提醒我一下我的代码风格偏好

7. 最佳实践建议Permalink
结构化记录：使用清晰的格式记录信息
分类存储：按技术栈、项目、习惯等分类记录
定期更新：当偏好或技术栈发生变化时及时更新
具体化描述：记录具体的解决方案而不是抽象概念