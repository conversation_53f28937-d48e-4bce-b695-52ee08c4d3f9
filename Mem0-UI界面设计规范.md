# Mem0-UI界面设计规范文档

## 1. 设计概述

### 1.1 设计原则
- **保持黑色主题**：与OpenMemory开发者工具定位一致
- **参考Mem0官方**：借鉴Activity页面的专业设计模式
- **开发者友好**：专注于功能性和易用性
- **差异化定位**：与Mem0官方白色主题形成区别

### 1.2 核心界面替换
**OpenMemory安装面板 → Mem0统计面板**
- 从安装引导界面转换为数据展示界面
- 保持深色主题的技术感
- 增加Mem0品牌色彩元素

## 2. 主面板设计规范

### 2.1 Mem0统计面板设计

#### 2.1.1 布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    Mem0 Memory Statistics                │
├─────────────────┬─────────────────┬─────────────────────┤
│   Total Memories │  Today Operations │   Avg Response Time │
│      1,234       │        23         │        45ms         │
├─────────────────┼─────────────────┼─────────────────────┤
│  Active Users   │  Graph Memories  │   Quick Actions     │
│       12        │       156        │   [Create] [Search] │
└─────────────────┴─────────────────┴─────────────────────┘
```

#### 2.1.2 统计卡片规范
```typescript
interface StatsCard {
  title: string;          // 卡片标题
  value: string | number; // 主要数值
  trend?: {               // 趋势指示器
    direction: 'up' | 'down' | 'stable';
    value: string;
    color: string;
  };
  icon?: string;          // 图标
}

// 样式规范
const StatsCardStyles = {
  background: '#1a1a1a',
  border: '1px solid #333',
  borderRadius: '8px',
  padding: '24px',
  
  title: {
    fontSize: '14px',
    color: '#888',
    marginBottom: '8px'
  },
  
  value: {
    fontSize: '32px',
    fontWeight: '600',
    color: '#00d4aa' // Mem0品牌色
  },
  
  trend: {
    fontSize: '12px',
    marginTop: '4px',
    positive: '#10b981',
    negative: '#ef4444',
    stable: '#888'
  }
};
```

### 2.2 Activity时间线设计

#### 2.2.1 时间线布局
```
┌─────────────────────────────────────────────────────────┐
│                    Recent Activity                       │
├─────────────────────────────────────────────────────────┤
│ 14:30:25  [SEARCH]  User searched for "preferences"  45ms│
│ 14:28:12  [ADD]     Created memory: User settings    32ms│
│ 14:25:03  [UPDATE]  Modified memory: Profile data    28ms│
│ 14:22:45  [DELETE]  Removed memory: Temp data        15ms│
│ 14:20:11  [GRAPH]   Created graph memory: Relations  67ms│
├─────────────────────────────────────────────────────────┤
│                    [Load More] [Filter]                 │
└─────────────────────────────────────────────────────────┘
```

#### 2.2.2 Activity记录规范
```typescript
interface ActivityRecord {
  id: string;
  timestamp: string;        // "14:30:25"
  operation: OperationType; // SEARCH, ADD, UPDATE, DELETE, GRAPH
  details: string;          // "User searched for 'preferences'"
  responseTime: string;     // "45ms"
  status: 'success' | 'error' | 'pending';
  userId?: string;
  metadata?: Record<string, any>;
}

type OperationType = 'SEARCH' | 'ADD' | 'UPDATE' | 'DELETE' | 'GRAPH_CREATE' | 'BATCH_UPDATE' | 'BATCH_DELETE';

// 操作类型颜色规范
const OperationColors = {
  SEARCH: '#1e40af',      // 蓝色
  ADD: '#059669',         // 绿色
  UPDATE: '#d97706',      // 橙色
  DELETE: '#dc2626',      // 红色
  GRAPH_CREATE: '#7c3aed', // 紫色
  BATCH_UPDATE: '#0891b2', // 青色
  BATCH_DELETE: '#be123c'  // 深红色
};
```

### 2.3 快速操作区域设计

#### 2.3.1 操作按钮规范
```typescript
interface QuickAction {
  id: string;
  label: string;
  icon: string;
  action: () => void;
  primary?: boolean;
  disabled?: boolean;
}

const quickActions: QuickAction[] = [
  {
    id: 'create-memory',
    label: 'Create Memory',
    icon: 'plus',
    action: () => navigateToCreate(),
    primary: true
  },
  {
    id: 'search-memories',
    label: 'Search Memories',
    icon: 'search',
    action: () => navigateToSearch()
  },
  {
    id: 'graph-memory',
    label: 'Graph Memory',
    icon: 'network',
    action: () => navigateToGraph()
  },
  {
    id: 'manage-users',
    label: 'Manage Users',
    icon: 'users',
    action: () => navigateToUsers()
  }
];
```

#### 2.3.2 按钮样式规范
```css
.quick-action-button {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-action-button:hover {
  background: #3a3a3a;
  border-color: #00d4aa;
}

.quick-action-button.primary {
  background: #00d4aa;
  color: #000000;
}

.quick-action-button.primary:hover {
  background: #00c49a;
}

.quick-action-button:disabled {
  background: #1a1a1a;
  color: #666;
  cursor: not-allowed;
}
```

## 3. 颜色系统规范

### 3.1 主色调定义
```typescript
const ColorSystem = {
  // 背景色
  background: {
    primary: '#0a0a0a',    // 主背景
    secondary: '#1a1a1a',  // 卡片背景
    tertiary: '#2a2a2a'    // 悬浮背景
  },
  
  // 边框色
  border: {
    primary: '#333',       // 主边框
    secondary: '#444',     // 次要边框
    active: '#00d4aa'      // 激活边框
  },
  
  // 文字色
  text: {
    primary: '#ffffff',    // 主文字
    secondary: '#888',     // 次要文字
    tertiary: '#666'       // 禁用文字
  },
  
  // 品牌色
  brand: {
    primary: '#00d4aa',    // Mem0主色
    secondary: '#00c49a',  // Mem0悬浮色
    accent: '#00b389'      // Mem0强调色
  },
  
  // 状态色
  status: {
    success: '#10b981',
    warning: '#d97706',
    error: '#dc2626',
    info: '#1e40af'
  }
};
```

### 3.2 渐变和阴影
```css
/* 卡片阴影 */
.card-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3),
              0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* 品牌渐变 */
.brand-gradient {
  background: linear-gradient(135deg, #00d4aa 0%, #00c49a 100%);
}

/* 悬浮效果 */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.4);
}
```

## 4. 响应式设计规范

### 4.1 断点定义
```typescript
const Breakpoints = {
  mobile: '320px',
  tablet: '768px',
  desktop: '1024px',
  wide: '1440px'
};
```

### 4.2 布局适配
```css
/* 移动端适配 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 平板适配 */
@media (min-width: 768px) and (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}

/* 桌面端适配 */
@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }
}
```

## 5. 动画和交互规范

### 5.1 过渡动画
```css
/* 标准过渡 */
.transition-standard {
  transition: all 0.2s ease;
}

/* 缓慢过渡 */
.transition-slow {
  transition: all 0.3s ease;
}

/* 快速过渡 */
.transition-fast {
  transition: all 0.1s ease;
}
```

### 5.2 加载状态
```typescript
// 加载骨架屏
const SkeletonCard = () => (
  <div className="skeleton-card">
    <div className="skeleton-title"></div>
    <div className="skeleton-value"></div>
    <div className="skeleton-trend"></div>
  </div>
);

// 加载动画
const LoadingSpinner = () => (
  <div className="loading-spinner">
    <div className="spinner"></div>
  </div>
);
```

这个设计规范确保了界面的一致性和专业性，同时保持了与OpenMemory的技术风格一致，并借鉴了Mem0官方的优秀设计模式。
