claude mcp add-json -s user promptx '{"type":"stdio","command":"npx","args":["-y","-f","--registry","https://registry.npmjs.org","dpml-prompt@beta","mcp-server"]}'
claude mcp add-json -s user context7 '{"type":"stdio","command":"npx","args":["-y", "@upstash/context7-mcp"]}'
claude mcp add-json -s user sequential-thinking '{"type":"stdio","command":"npx","args":["-y","@modelcontextprotocol/server-sequential-thinking"]}'
claude mcp add-json -s user shrimp-task-manager '{"type":"stdio","command":"node","args":["/root/mcp-shrimp-task-manager/dist/index.js"],"env":{"DATA_DIR": "/root/mcp-shrimp-task-manager/data","TEMPLATES_USE": "zh","ENABLE_GUI": "true"}}'
claude mcp add-json -s user firecrawl-mcp '{"type":"stdio","command":"npx","args":["-y", "firecrawl-mcp"],"env":{"FIRECRAWL_API_URL": "http://10.10.1.81:3002","FIRECRAWL_API_KEY":"","FIRECRAWL_RETRY_MAX_ATTEMPTS":"5","FIRECRAWL_RETRY_INITIAL_DELAY":"2000","FIRECRAWL_RETRY_MAX_DELAY": "30000","FIRECRAWL_RETRY_BACKOFF_FACTOR": "3"}}'
claude mcp add -s user --transport http chrome-mcp http://10.10.1.254:12306/mcp



echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-BqJ0ZoveuIqhRqhj67buf3I7Rq6i3eycn6enxk73pwzLk7BR' >> ~/.bash_profile
echo -e '\n export ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.bash_profile
echo -e '\n export ANTHROPIC_AUTH_TOKEN=-sk-BqJ0ZoveuIqhRqhj67buf3I7Rq6i3eycn6enxk73pwzLk7BR' >> ~/.bashrc
echo -e '\n export ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.bashrc
echo -e '\n export ANTHROPIC_AUTH_TOKEN=sk-BqJ0ZoveuIqhRqhj67buf3I7Rq6i3eycn6enxk73pwzLk7BR' >> ~/.zshrc
echo -e '\n export ANTHROPIC_BASE_URL=https://anyrouter.top' >> ~/.zshrc



claude mcp remove firecrawl-mcp