应用开发专家，精通python

基于以上，需要对它进行二次开发以支持本地自托管的部署，以下是核心需求：
1、自定义API_URL指向本地服务，API key生成
2、自定义OPENAI的API_URL以及KEY，模型gpt-4o-mini text-embedding-3-small
3、保持Mem0核心模块、功能的不变
4、保持API路由的方式不变
6、在linux服务器上部署，需要自动化可交互脚本来方便部署管理
7、强调：直接读取源码文件修改并编辑，修改调整禁止在windows本地测试和运行，不要新建不规范的命名文件
8、请认真查阅项目相关文件夹内容doc、evaluation、mem0、examples、mem0-ts、vector_stores、vercel-ai-sdk，源码地址https://github.com/mem0ai/mem0/tree/main ，官方文档https://docs.mem0.ai/quickstart，官方sdk地址，https://docs.mem0.ai/open-source/python-quickstart希望对你有所帮助。

理解以上内容后给出一个二次开发方案，不用生成代码。

强调解决问题修复规则：
1、认真查看doc、mem0、examples、mem0-ts、vector_stores、vercel-ai-sdk等文件夹文件，以及官方文档https://docs.mem0.ai/open-source/quickstart、项目地址https://github.com/mem0ai/mem0，查找解决方案最终解决问题。
2、禁止以简化版的思路来修复问题
3、能修改源码就不要新建文件
4、能修改源码就不要用修复脚本
5、不要做一些无关紧要的修改增加代码量
6、不要用简化功能的方式取代模块功能
7、代码只考虑linux,不在Windows运行
8、请分段直接读取源码文件修改并编辑
9、必须使用中文回复
10、请分段创建代码文件

现在根据你的思路，按流程一步一步开发修改源码，最终我来上传linux服务器部署
遵守规则后，等我下一步指令


记忆项目所有功能、需求、内容、规则

现在根据你的思路，按流程一步一步开发修改源码，最终我来上传linux服务器部署

1、quick_deploy.sh创建API KEY失败，缺少日志查看、重启、跳过基础配置直接重构容器的便捷命令
2、mem0_manager.py没有清理缓存、清楚未使用的容器等功能，且监控服务不能正常使用、
3、检查完善两个脚本

检查当前项目的需求是否满足：
1、必须支持所有mem0的功能且必须包含V2 API
2、需要管理记忆的功能（包括列表，删改）
3、利用官方的mem0ai核心
4、请认真查阅项目相关文档，源码地址https://github.com/mem0ai/mem0/tree/main  ，官方文档https://docs.mem0.ai/quickstart ，官方sdk地址https://docs.mem0.ai/open-source/python-quickstart 
4、理解源码的添加、检索记忆功能实现的底层逻辑
5、请认真参考官方的docs、examples、test等文件夹中的内容

理解之后先回复理解的结果

根据目前的项目进度，写一个测试mom0的所有功能模块是否正常的脚本，分段创建文件
1、每个项目模块至少三条内容
2、内容多元化，复杂化
3、不要对openai模型调用做各种负载压力测试
4、测试完后删除测试数据
5、生成一个测试日志方便修复报错
6、调用已经设置好的.env环境变量参数
7、包括模拟插件mem0-dify-integrated调用
8、先测试路由API正常后再进行功能的测试

如果超过最大令牌限制，请分段创建文件

我现在需要依据这个项目添加记忆后数据来开发一个前端web应用，方便管理添加的记忆：
1、获取服务端的记忆数据
2、有分类列表，记忆的内容包含、用户、时间、ID
3、查找
4、添加/删除
5、备份

提供的你思路
