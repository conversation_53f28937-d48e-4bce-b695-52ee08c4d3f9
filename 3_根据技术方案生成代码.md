基于技术方案核心要点，使用以下MCP工具来完成任务：
sequentialthinking
plan_task
firecrawl_extract
firecrawl_scrape
research_mode
# 目标
请你按照设计好的方案，生成代码。

# 背景知识
为了帮助你更好的生成代码，我已为你提供：
（1）项目代码
（2）需求文档：《需求.md》
（3）技术方案：《Mem0二次开发技术方案设计.md》
（4）项目理解文档:《项目梳理文档.md》

# 核心任务
## 1. 文档分析与理解阶段  
在动手编写代码前完成以下分析：  
- 需求匹配度检查：  
  - 深入理解需求文档和方案设计文档，确认《Mem0二次开发技术方案设计.md》与《需求.md》在功能点、输入输出、异常场景上的完全一致性。  
  - 若发现矛盾请立即标记并提交备注。  
- 代码架构理解：  
  - 深入理解项目梳理文档和现有代码库的分层结构，确定新功能的插入位置。  
  - 列出可复用的工具类、异常处理机制和公共接口（如`utils.py`、`ErrorCode`枚举类）。  

## 2. 代码生成阶段
如果你已明确需求和技术方案，请你完成代码编写工作。

# 要求
1. 你必须遵循以下核心原则：
（1）你生成的代码必须参考当前项目的代码风格。
（2）如项目已有可用方法，必须考虑复用、或在现有方法上扩展、或进行方法重载，保证最小粒度改动，减少重复代码。
（3）你生成的代码必须与项目已有代码逻辑保持一致，不能有逻辑错误。
    - 函数命名必须与项目已有方法保持一致
      - 参数必须与项目已有方法保持一致
      - 返回值必须与项目已有方法保持一致
（4）修改代码请重构容器再进行修复验证。不能使用修复脚本使用旧容器进行修复验证，不能使用简化功能、优雅修复、屏蔽错误日志方法、使用临时变量、临时文件进行修复验证。
2. 你生成的代码必须符合《python统一开发编程规范》中定义的规范。

# 输出
请你使用plan_task生成完整详细的任务计划，注意任务依赖合理性、分类合理、任务描述清晰。每个完成的任务必须将这本次修复的问题核心、过程和结果增量并且更新到.cursor/docs文件夹下的文件《技术审查与修复报告.md》总结