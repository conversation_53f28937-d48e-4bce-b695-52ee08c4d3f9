### 一、代码风格规范

1. 严格限制方法长度（≤50行），超长立即拆分子方法
2. 提供完整的CRUD示例代码作为风格基准（Controller/Service/DAO分层实现）

### 二、核心注意事项

1. 标注框架约束和版本特性（如SpringBoot 2.7+JPA事务注解）
2. 记录关键业务逻辑红线（如支付模块必须幂等）
3. 标记技术风险点（如跨服务调用需熔断）

### 三、功能实现流程

1. **单点聚焦原则**
   - 每次仅实现1个独立功能点
   - 完成后立即口头总结："完成[功能]，缺失[X]，需补充[Y]"
2. **SQL优先验证**
   - 重要SQL先实现并执行验证
   - 确认结果集完整性与性能指标

### 四、复查机制

1. **记忆清零复查**
   - 完成功能后清空工作记忆
   - 对照大纲重新走查逻辑链条
   - 重点检查异常分支和边界条件
2. **逻辑完整性检查**
   - 验证输入输出数据流
   - 确认事务一致性
   - 测试并发场景

### 五、文档管理

1. **分层存储策略

   ```
   主文档/
   ├─ 架构图 & 核心流程    // 仅关键信息
   └─ 子目录/
      ├─ 模块A细节.md     // 详细实现
      └─ SQL说明.md      // 复杂SQL文档
   ```

2. **即时更新规则**

   - 代码提交时同步更新文档
   - 删除过时内容防止膨胀

### 六、进度跟踪

1. 标记完成状态：
   - ✅ 功能名 (100%)
   - 🟡 功能名 (60%) + 未完成部分说明
2. 每日更新完成计划表

### 七、阻塞问题处理

1. **TODO标记规范

   ```
   // TODO: [需求描述] 示例：分布式锁需手动实现 
   // 预期方案：RedissonRLock
   ```

2. 隔离处理原则：

   - 将阻塞点拆分为独立方法
   - 保留完整需求描述上下文

### 八、代码质量保障

1. 异常分支显式标注：`// ! ALERT: 空订单处理未实现`

2. 关键位置添加防御性日志：

   ```
   log.debug("用户{}权限校验", userId); // 必须输出关键参数
   ```

### 九、模块组织标准

1. 树形包结构：

   ```
   src/
   ├─ order/                  // 业务域
   │  ├─ controller/          // API层
   │  ├─ service/             // 业务逻辑
   │  └─ model/               // 数据对象
   └─ payment/                // 支付域
   ```

### 十、CRUD示例要素

1. 统一包含：
   - 参数校验注解 `@Valid`
   - 响应体封装 `ResponseEntity<T>`
   - 错误码常量 `ErrorCode.NOT_FOUND`

------

**逻辑完备性保障**：
通过「记忆清零复查 + TODO隔离 + 即时文档」三角机制，确保：

1. 每次迭代逻辑闭环
2. 技术债务显式标记
3. 核心文档永不过时